import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Star, User } from 'lucide-react';

interface Feedback {
  id?: string | number;
  name?: string;
  rating?: number;
  comments?: string;
  created_at?: string;
}

interface FeedbackCarouselProps {
  feedbacks?: Feedback[];
}

const FeedbackCarousel: React.FC<FeedbackCarouselProps> = ({
  feedbacks = [],
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const feedbacksPerView = 1;

  const totalPages = Math.ceil(feedbacks.length / feedbacksPerView);

  const getCurrentFeedbacks = () => {
    const start = currentIndex * feedbacksPerView;
    const end = start + feedbacksPerView;
    return feedbacks.slice(start, end);
  };

  const goToPrevious = () => {
    setCurrentIndex(prev => (prev > 0 ? prev - 1 : totalPages - 1));
  };

  const goToNext = () => {
    setCurrentIndex(prev => (prev < totalPages - 1 ? prev + 1 : 0));
  };

  const isFirstFeedback = currentIndex === 0;
  const isLastFeedback = currentIndex === totalPages - 1;

  const renderStarRating = (rating: number) => {
    return (
      <div className='flex items-center gap-1'>
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-[#f09e22] fill-current' : 'text-gray-400'
            }`}
          />
        ))}
        <span className='ml-1 text-sm font-medium text-gray-700'>
          {rating}/5
        </span>
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Date unavailable';
    }
  };

  if (feedbacks.length === 0) {
    return (
      <div className='bg-slate-100 p-7 md:p-7 md:mb-0 items-center flex flex-1 flex-col  justify-start rounded-xl gap-2'>
        <img
          src='/Images/stars.svg'
          alt='Currently NO Feedbacks Available'
          className='p-2 w-20'
        />
        <p className='text-base font-medium text-slate-500'>
          No Previous Feedbacks for you
        </p>
        <p className='text-sm text-slate-400 text-center'>
          Your amazing feedback will appear here once you start getting reviews!
        </p>
      </div>
    );
  }

  return (
    <div className='relative md:mb-0'>
      {}

      <div className='bg-white rounded-xl p-4 min-h-[280px] relative overflow-hidden'>
        {}
        <div className=''>
          <div className='h-full relative'>
            {getCurrentFeedbacks().map((feedback, index) => (
              <div
                key={feedback?.id || `feedback-${currentIndex}-${index}`}
                className='bg-[#F2F2F2] p-4 px-14 rounded-lg border border-gray-100 flex flex-col justify-center min-h-[220px] shadow-xl relative h-full'
              >
                {}
                {totalPages > 1 && (
                  <>
                    <button
                      onClick={goToPrevious}
                      className={`absolute left-2 top-1/2 -translate-y-1/2 z-10 rounded-full p-2 shadow-md transition-all duration-300 hover:shadow-lg ${
                        isFirstFeedback
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-nursery-blue hover:bg-nursery-darkBlue'
                      }`}
                      aria-label='Previous feedbacks'
                      style={{ marginTop: '-12px' }}
                      disabled={isFirstFeedback}
                    >
                      <ChevronLeft className='w-5 h-5 text-white' />
                    </button>
                    <button
                      onClick={goToNext}
                      className={`absolute right-2 top-1/2 -translate-y-1/2 z-10 rounded-full p-2 shadow-md transition-all duration-300 hover:shadow-lg ${
                        isLastFeedback
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-nursery-blue hover:bg-nursery-darkBlue'
                      }`}
                      aria-label='Next feedbacks'
                      style={{ marginTop: '-12px' }}
                      disabled={isLastFeedback}
                    >
                      <ChevronRight className='w-5 h-5 text-white' />
                    </button>
                  </>
                )}
                {}
                <div className='flex flex-col justify-between items-start mb-3'>
                  <div className='flex flex-row items-center gap-2 mb-2 w-full'>
                    <div className='bg-nursery-blue p-1.5 rounded-full'>
                      <User size={20} className='text-white' />
                    </div>
                    <h3 className='font-semibold text-gray-800 flex-1'>
                      {feedback?.name || 'Anonymous'}
                    </h3>
                  </div>
                  <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2'>
                    <div>{renderStarRating(feedback?.rating || 0)}</div>
                    <p className='text-xs text-gray-500'>
                      {formatDate(feedback?.created_at || '')}
                    </p>
                  </div>
                </div>

                {}
                <div className='flex-1'>
                  <p className='text-gray-700 text-sm leading-relaxed max-h-[100px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent'>
                    {feedback?.comments || 'No comments provided'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {}
        <div className='flex justify-center'>
          {feedbacks.length > 0 && (
            <div className='mt-3 text-sm font-semibold text-nursery-darkBlue'>
              <span>
                Showing {currentIndex * feedbacksPerView + 1} of{' '}
                {feedbacks.length} feedbacks
              </span>
            </div>
          )}
        </div>
        {totalPages > 1 && (
          <div className='flex justify-center items-center mx-auto mt-4 gap-2'>
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={`pagination-${index}`}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  index === currentIndex
                    ? 'bg-nursery-darkBlue w-6'
                    : 'bg-gray-600 hover:bg-gray-400'
                }`}
                aria-label={`Go to page ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FeedbackCarousel;
