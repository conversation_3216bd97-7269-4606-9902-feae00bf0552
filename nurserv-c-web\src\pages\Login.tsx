import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Logo from '../components/Logo';
import { usePhoneLoginMutation } from '@/store/api/apiSlice';
import {
  showErrorToast,
  showSuccessToast,
  handleApiError,
} from '@/utils/toast';
import { Eye, EyeOff } from 'lucide-react';

const Login = () => {
  const navigate = useNavigate();
  const [phone_number, setPhone] = useState('');
  const [phoneLogin, { isLoading }] = usePhoneLoginMutation();
  const [password, setPassword] = useState('');
  const [phoneNumberError, setPhoneNumberError] = useState<string>('');
  const [passwordError, setPasswordError] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = (): boolean => {
    let hasErrors = false;

    const numbersOnly = phone_number.replace('+91', '');
    if (!/^[0-9]{10}$/.test(numbersOnly)) {
      setPhoneNumberError('Please Enter a Valid 10-digit Mobile number.');
      hasErrors = true;
    } else {
      setPhoneNumberError('');
    }

    if (!password) {
      setPasswordError('Password is required.');
      hasErrors = true;
    } else {
      setPasswordError('');
    }

    return !hasErrors;
  };

  const storeAuthTokens = (tokens: {
    accessToken?: string;
    idToken?: string;
    refreshToken?: string;
  }) => {
    if (tokens?.accessToken) {
      localStorage.setItem('token', tokens.accessToken);
    }
    if (tokens?.idToken) {
      localStorage.setItem('idToken', tokens.idToken);
    }
    if (tokens?.refreshToken) {
      localStorage.setItem('refreshToken', tokens.refreshToken);
    }
  };

  const handleLoginSuccess = (response: {
    tokens?: { accessToken?: string; idToken?: string; refreshToken?: string };
    user?: {
      username?: string;
      cognito_id?: string;
      given_name?: string;
      address?: string;
    };
  }) => {
    storeAuthTokens(response.tokens);

    if (response?.user?.username) {
      localStorage.setItem('userEmail', response.user.username);
    }

    showSuccessToast('Login successful');
    navigate('/home', {
      state: {
        user_id: response.user?.cognito_id,
        given_name: response.user?.given_name,
        username: response.user?.username,
        address: response.user?.address,
      },
    });
  };

  const handleLoginError = (error: unknown) => {
    console.error('Login error:', error);

    const apiError = error as {
      data?: { error?: string };
      message?: string;
      status?: number;
    };
    const errorMessage =
      apiError?.data?.error || apiError?.message || 'Login failed';
    const statusCode = apiError?.status;

    switch (statusCode) {
      case 404:
        setPhoneNumberError(
          'User not found. Please check your phone number or register.'
        );
        break;
      case 401:
        setPasswordError('Incorrect phone number or password.');
        break;
      case 403:
        showErrorToast('Account not verified. Please verify your account.');
        break;
      default:
        handleApiError(error, {
          400: errorMessage || 'Invalid input. Please check your details.',
        });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      sessionStorage.clear();
      localStorage.clear();
      const response = await phoneLogin({ phone_number, password }).unwrap();

      if (response) {
        handleLoginSuccess(response);
      }
    } catch (error: unknown) {
      handleLoginError(error);
    }
  };

  const handlePhoneNumber = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    if (!value.startsWith('+91')) {
      value = '+91' + value.replace(/^\+91/, '');
    }
    setPhone(value);
  };

  const handlePassword = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setPassword(password);
    if (password && password.length < 6) {
      setPasswordError('Password length should be at least 6 characters.');
    } else {
      setPasswordError('');
    }
  };

  return (
    <div className='relative w-full overflow-hidden min-h-screen flex flex-col'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>
      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
      <div className='relative z-10 h-full w-full flex-1 flex flex-col items-center justify-center pt-10 px-6 gap-3'>
        <Logo />
        {}
        <div className='w-full max-w-md bg-white rounded-xl p-6 shadow-lg'>
          <h2 className='text-2xl font-bold mb-6 text-nursery-blue'>Sign In</h2>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <Input
              type='text'
              placeholder='Enter Mobile Number'
              value={phone_number}
              onChange={handlePhoneNumber}
              className='w-full h-11 text-base'
            />
            {phoneNumberError && (
              <p className='text-sm text-red-500'>{phoneNumberError}</p>
            )}

            <div className='relative'>
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder='Enter Password'
                value={password}
                onChange={handlePassword}
                className='w-full h-11 text-base'
              />
              <Button
                type='button'
                onClick={() => {
                  setShowPassword(!showPassword);
                }}
                className='bg-transparent absolute right-3 top-1 text-gray-500 hover:bg-transparent hover:text-gray-900 '
              >
                {showPassword ? <Eye size={20} /> : <EyeOff size={20} />}
              </Button>

              {passwordError && (
                <p className='text-sm text-red-500 mt-3'>{passwordError}</p>
              )}
            </div>

            <div className='flex justify-end'>
              <button
                type='button'
                className='text-nursery-blue hover:text-nursery-darkBlue text-base'
                onClick={() => navigate('/forgotpassword')}
              >
                Forgot Password?
              </button>
            </div>

            <Button
              type='submit'
              className='w-full h-11 bg-[#5EB2CC] hover:bg-[#4996B5] text-white text-lg font-medium'
              disabled={isLoading}
            >
              Sign In
            </Button>
          </form>

          <div className='mt-6 text-center'>
            <p className='text-base'>
              Don&apos;t have an account?{' '}
              <button
                onClick={() => navigate('/signup')}
                className='text-nursery-blue hover:text-nursery-darkBlue font-medium'
              >
                Register
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
