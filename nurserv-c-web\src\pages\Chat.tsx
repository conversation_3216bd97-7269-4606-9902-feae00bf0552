import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Search, User } from 'lucide-react';
import { useGetBookingsByCustomerQuery, Booking } from '@/store/api/apiSlice';
import { useGetProfileImageByUserIdQuery } from '@/store/api/nurseApiSlice';
import ResponsiveLoader from '@/components/Loader';
import ChatModal from '@/components/ChatModal';
import Footer from '@/components/Footer';

const NurseProfileImage = ({
  nurseCognitoId,
  nurseName,
}: {
  nurseCognitoId?: string;
  nurseName?: string;
}) => {
  const {
    data: profileImageData,
    isLoading: isProfileImageLoading,
    isError: isProfileImageError,
  } = useGetProfileImageByUserIdQuery(nurseCognitoId || '', {
    skip: !nurseCognitoId,
  });

  if (
    isProfileImageLoading ||
    isProfileImageError ||
    !profileImageData?.profile_image?.signed_url
  ) {
    return (
      <div className='w-9 h-9 md:w-10 md:h-10 bg-nursery-blue rounded-full flex items-center justify-center'>
        {nurseName ? (
          <span className='text-white text-sm md:text-base font-semibold'>
            {nurseName.charAt(0).toUpperCase()}
          </span>
        ) : (
          <User className='w-5 h-5 md:w-6 md:h-6 text-white' />
        )}
      </div>
    );
  }

  return (
    <div className='w-9 h-9 md:w-10 md:h-10 rounded-full overflow-hidden'>
      <img
        src={profileImageData.profile_image.signed_url}
        alt={`${nurseName || 'Nurse'} profile`}
        className='w-full h-full object-cover'
        onError={e => {
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) {
            fallback.style.display = 'flex';
          }
        }}
      />
      <div
        className='w-9 h-9 md:w-10 md:h-10 bg-nursery-blue rounded-full items-center justify-center'
        style={{ display: 'none' }}
      >
        {nurseName ? (
          <span className='text-white text-sm md:text-base font-semibold'>
            {nurseName.charAt(0).toUpperCase()}
          </span>
        ) : (
          <User className='w-5 h-5 md:w-6 md:h-6 text-white' />
        )}
      </div>
    </div>
  );
};

const Chat = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNurse, setSelectedNurse] = useState<{
    nurse_cognitoId: string;
    nurse_given_name: string;
  } | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);

  const userId = localStorage.getItem('userId');

  const {
    data: bookingResponse,
    isLoading: bookingLoading,
    error: bookingError,
  } = useGetBookingsByCustomerQuery(userId, {
    skip: !userId,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const bookingsData = bookingResponse?.bookings || [];

  const uniqueNursesMap = new Map();

  bookingsData.forEach((booking: Booking) => {
    if (
      (booking.booking_status === 'Accepted' ||
        booking.booking_status === 'Completed') &&
      !uniqueNursesMap.has(booking.nurse_cognitoId)
    ) {
      uniqueNursesMap.set(booking.nurse_cognitoId, booking);
    }
  });

  const uniqueNurses = Array.from(uniqueNursesMap.values());

  const displayedNurses = uniqueNurses.filter((booking: Booking) =>
    booking.nurse_given_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-8 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed '>
          <img
            src='/Images/bg4.png'
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        <div className=' absolute z-10 flex items-center top-5 '>
          <button onClick={() => navigate(-1)} className='mr-4'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Chat</h1>
        </div>
      </header>

      {}
      <main className='flex-1'>
        {}
        <div className='mt-5 w-11/12 md:w-5/12 mx-auto'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5' />
            <input
              type='text'
              placeholder='Search a nurses name'
              className='w-full pl-10 pr-4 py-3 border-gray-300 bg-[#F2F2F2] rounded-md text-sm text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nursery-blue'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className='w-11/12 md:w-10/12 mx-auto mt-5 space-y-4'>
          {bookingLoading && <ResponsiveLoader />}
          {bookingError && (
            <div className='text-center text-red-500 py-8'>
              Error loading bookings.
            </div>
          )}
          {!bookingLoading && !bookingError && bookingsData.length === 0 && (
            <div className='text-center text-gray-500 py-8'>
              No bookings found.
            </div>
          )}
          <p className='md:text-lg text-md text-nursery-darkBlue font-semibold '>
            Chat with Your Nurse
          </p>
          {displayedNurses.map((booking: Booking) => (
            <button
              key={booking.nurse_cognitoId}
              className='flex w-full items-center bg-[#F2F2F2] gap-3 md:p-3 p-2 border-b rounded-lg shadow-lg hover:bg-gray-100 transition-colors'
              onClick={() => {
                setSelectedNurse({
                  nurse_cognitoId: booking.nurse_cognitoId,
                  nurse_given_name: booking.nurse_given_name,
                });
                setIsChatOpen(true);
              }}
            >
              <NurseProfileImage
                nurseCognitoId={booking.nurse_cognitoId}
                nurseName={booking.nurse_given_name}
              />
              <span className='font-semibold text-lg'>
                {booking.nurse_given_name}
              </span>
            </button>
          ))}
        </div>
      </main>
      <Footer />
      <ChatModal
        selectedNurse={selectedNurse}
        isOpen={isChatOpen}
        onClose={() => {
          setIsChatOpen(false);
          setSelectedNurse(null);
        }}
      />
    </div>
  );
};

export default Chat;
