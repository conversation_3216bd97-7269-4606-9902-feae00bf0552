import { useNavigate } from 'react-router-dom';
import {
  showErrorToast,
  showSuccessToast,
  handleApiError,
} from '@/utils/toast';
import { useUpdateCustomerLocationMutation } from '../store/api/apiSlice';
import LocationCard from '../components/LocationCard';

const LocationSelection = () => {
  const navigate = useNavigate();
  const [updateLocation, { isLoading }] = useUpdateCustomerLocationMutation();

  const handleLocationSelect = async (locationData: {
    latitude: number;
    longitude: number;
    address: string;
  }) => {
    const token = localStorage.getItem('idToken');
    if (!token || token === 'null' || token === 'undefined') {
      showErrorToast('Authentication required. Please log in again.');
      localStorage.removeItem('idToken');
      navigate('/login');
      return;
    }

    try {
      const result = await updateLocation({
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        address: locationData.address,
      }).unwrap();

      if (result.message === 'Location updated successfully') {
        showSuccessToast('Location updated successfully!');

        navigate('/home', {
          state: {
            given_name: result.user?.given_name,
            username: result.user?.username,
            address: result.user?.address,
            customer_set_location: true,
          },
        });
      }
    } catch (error: unknown) {
      console.error('Location update error:', error);

      const apiError = error as {
        data?: { error?: string; message?: string };
        message?: string;
        status?: number;
      };
      const errorMessage =
        apiError?.data?.error || apiError?.data?.message || apiError?.message;
      const statusCode = apiError?.status;

      switch (statusCode) {
        case 404:
          if (errorMessage?.includes('User not found')) {
            showErrorToast('User not found. Please log in again.');
            localStorage.removeItem('idToken');
            navigate('/login');
          } else {
            showErrorToast('Failed to update user location');
          }
          break;
        case 403:
          showErrorToast('Access denied. Please log in again.');
          localStorage.removeItem('idToken');
          navigate('/login');
          break;
        case 400:
          if (
            errorMessage?.includes('Latitude') ||
            errorMessage?.includes('Longitude')
          ) {
            showErrorToast(
              'Invalid location coordinates. Please select a valid location.'
            );
          } else {
            showErrorToast(
              errorMessage || 'Invalid location data. Please try again.'
            );
          }
          break;
        default:
          handleApiError(error, {
            [statusCode]: 'Failed to update location. Please try again.',
          });
      }
    }
  };

  return (
    <div className='min-h-screen flex flex-col relative p-4'>
      {}
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>
      {}
      <div className='absolute inset-0 bg-black bg-opacity-20 z-0' />

      <div className='flex-1 flex flex-col items-center justify-start pt-8 relative z-10'>
        <img
          src='/Images/Logo.svg'
          alt='Nurses team'
          className='w-[200px] h-[60px] object-cover bg-transparent animate-fade-in max-w-md mx-auto mb-3 -mt-8'
        />
        <LocationCard
          onLocationSelect={handleLocationSelect}
          isLoading={isLoading}
          buttonText='Confirm Location'
          title='Select Your Location'
        />
      </div>
    </div>
  );
};

export default LocationSelection;
